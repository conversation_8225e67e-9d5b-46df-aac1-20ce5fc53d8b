{"name": "@autodev/context-worker", "version": "0.6.1", "description": "AutoDev Context Worker - Code analysis and context extraction library", "main": "./dist/index.js", "module": "./dist/index.esm.js", "types": "./dist/index.d.ts", "bin": {"autodev": "./dist/autodev-context-worker.js"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "start": "node dist/autodev-context-worker.js", "test": "nx vite:test --run", "test:watch": "nx vite:test", "test:coverage": "nx vite:test run --coverage", "prepublishOnly": "pnpm run build"}, "keywords": [], "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "@mozilla/readability": "0.6.0", "@octokit/rest": "^20.1.1", "@types/inquirer": "^9.0.8", "@types/node-fetch": "^2.6.12", "@unit-mesh/treesitter-artifacts": "^1.7.2", "cheerio": "^1.0.0-rc.12", "commander": "^13.1.0", "csv-parse": "^5.5.5", "graphology": "^0.26.0", "graphology-types": "^0.24.8", "iconv-lite": "^0.6.3", "ignore": "^5.3.1", "inquirer": "^12.6.0", "inversify": "^6.0.2", "isbinaryfile": "^5.0.2", "js-tiktoken": "^1.0.11", "js-yaml": "^4.1.0", "jsdom": "^24.0.0", "lodash": "^4.17.21", "lru-cache": "^10.2.2", "node-fetch": "^2.7.0", "node-machine-id": "^1.1.12", "protobufjs": "^7.5.1", "reflect-metadata": "^0.2.2", "remark": "^15.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "velocityjs": "^2.0.6", "web-tree-sitter": "^0.22.2", "@autodev/worker-core": "workspace:*", "@autodev/worker-protobuf": "workspace:*"}, "author": "Phodal HUANG<<EMAIL>>", "license": "MIT", "packageManager": "pnpm@10.10.0", "devDependencies": {"@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@types/mocha": "^10.0.10", "@types/node": "^20.17.45", "@vitest/coverage-v8": "^1.0.4", "rollup": "^4.40.2", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-string-import": "^1.2.5", "typescript": "^5.8.3", "vitest": "^1.0.4"}}