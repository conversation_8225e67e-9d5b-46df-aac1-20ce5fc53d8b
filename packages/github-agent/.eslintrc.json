{"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "env": {"node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/ban-ts-comment": "off"}, "ignorePatterns": ["dist/**", "node_modules/**", "bin/**", "scripts/**", "*.js"]}