{"name": "@autodev/github-agent", "version": "0.6.1", "description": "AutoDev GitHub Issue Agent MCP Server - Analyze GitHub issues with code context", "main": "dist/index.js", "bin": {"autodev-github-server": "./bin/server.js", "autodev-ai-agent": "./bin/agent.js", "autodev-analyze-issue": "./bin/analyze-issue.js"}, "module": "dist/index.esm.js", "type": "commonjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist", "bin", "scripts", "package.json", ".env.example", "README.md"], "scripts": {"build": "rollup -c", "build:clean": "pnpm run clean && pnpm run build", "dev": "rollup -c -w", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:analyzer": "jest --testPathPattern=project-context-analyzer.test.ts", "test:unit": "jest __tests__/unit", "test:integration": "jest __tests__/integration", "test:e2e": "jest __tests__/e2e", "test:verbose": "jest --verbose", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "rimraf dist node_modules/.cache", "clean:all": "rimraf dist node_modules/.cache materials *.log *.json", "cleanup": "node -e \"require('./dist/utils/cleanup.js').CleanupManager.runCleanup()\"", "cleanup:dry": "node -e \"require('./dist/utils/cleanup.js').CleanupManager.runCleanup({dryRun: true})\"", "prepublishOnly": "pnpm run build:clean", "analyze-issue": "node bin/analyze-issue.js"}, "keywords": ["mcp", "model-context-protocol", "github", "issues", "code-analysis", "typescript"], "author": "AutoDev authors", "license": "MIT", "dependencies": {"@ai-sdk/openai": "^1.0.0", "@autodev/context-worker": "workspace:*", "@autodev/worker-core": "workspace:*", "@modelcontextprotocol/sdk": "^1.11.1", "@octokit/rest": "^20.1.2", "@types/cheerio": "^1.0.0", "@vscode/ripgrep": "^1.15.9", "ai": "^4.0.0", "cheerio": "^1.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "turndown": "^7.2.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@octokit/types": "^14.0.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/express": "^5.0.1", "@types/jest": "^29.5.11", "@types/node": "^20.17.46", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "rollup": "^4.9.1", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-typescript2": "^0.36.0", "ts-jest": "^29.1.1", "tsx": "^4.7.0", "typescript": "^5.3.3", "zod": "^3.24.4"}, "publishConfig": {"access": "public"}}