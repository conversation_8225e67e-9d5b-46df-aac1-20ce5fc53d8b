{"compilerOptions": {"outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": false, "sourceMap": true, "target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "removeComments": true, "preserveConstEnums": false, "importsNotUsedAsValues": "remove"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "materials/**/*", "*.log"]}